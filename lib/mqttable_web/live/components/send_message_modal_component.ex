defmodule MqttableWeb.SendMessageModalComponent do
  @moduledoc """
  A reusable modal component for sending MQTT messages.

  This component provides a form for composing and sending MQTT messages with support for:
  - Client selection with automatic fallback
  - Form state persistence across modal open/close cycles
  - MQTT 5.0 properties and user properties
  - Click-outside-to-close functionality
  """

  use MqttableWeb, :live_component

  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:publish_form, fn -> default_publish_form() end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    Logger.debug(
      "SendMessageModalComponent update called with assigns keys: #{inspect(Map.keys(assigns))}"
    )

    # Get current form state or use default
    current_form = socket.assigns[:publish_form] || default_publish_form()

    # Smart client selection logic
    updated_form =
      if assigns[:show_modal] do
        smart_client_selection(current_form, assigns[:active_broker_name])
      else
        current_form
      end

    socket =
      socket
      |> assign(assigns)
      |> assign(:publish_form, updated_form)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Send Message Modal -->
      <dialog id="send-message-modal" class={"modal #{if @show_modal, do: "modal-open", else: ""}"}>
        <div class="modal-backdrop" phx-click="close_modal" phx-target={@myself}></div>
        <div class="modal-box max-w-md ml-auto mr-6 mt-6 mb-6 h-fit max-h-[calc(100vh-3rem)] send-message-modal">
          <!-- Modal Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold flex items-center">
              <.icon name="hero-paper-airplane" class="size-5 mr-2" /> Send MQTT Message
            </h3>
            <button
              class="btn btn-sm btn-circle btn-ghost"
              phx-click="close_modal"
              phx-target={@myself}
            >
              ✕
            </button>
          </div>
          
    <!-- Modal Content -->
          <div class="space-y-4 overflow-y-auto max-h-[calc(100vh-8rem)]">
            <.form
              for={@publish_form}
              phx-submit="send_message"
              phx-target={@myself}
              class="space-y-4"
            >
              <!-- Client ID Selection -->
              <div class="form-control w-full">
                <label class="label">
                  <span class="label-text font-medium">
                    Client <span class="text-error">*</span>
                  </span>
                </label>
                <select
                  name="client_id"
                  class="select select-bordered w-full"
                  required
                  phx-change="client_selection_changed"
                  phx-target={@myself}
                >
                  <option value="">Select a connected client</option>
                  <%= for client <- get_connected_clients(@active_broker_name || "") do %>
                    <option
                      value={client.client_id}
                      selected={@publish_form["client_id"] == client.client_id}
                    >
                      {client.client_id} (MQTT {client.mqtt_version || "5.0"})
                    </option>
                  <% end %>
                </select>
              </div>
              
    <!-- Topic -->
              <div class="form-control w-full">
                <label class="label">
                  <span class="label-text font-medium">
                    Topic <span class="text-error">*</span>
                  </span>
                </label>
                <input
                  type="text"
                  name="topic"
                  value={@publish_form["topic"]}
                  placeholder="Enter topic (e.g., 'device/sensor/temperature')"
                  class="input input-bordered w-full"
                  required
                  phx-change="form_field_changed"
                  phx-target={@myself}
                />
              </div>
              
    <!-- Payload -->
              <div class="form-control w-full">
                <label class="label">
                  <span class="label-text font-medium">Payload</span>
                </label>
                <textarea
                  name="payload"
                  placeholder="Enter message payload"
                  class="textarea textarea-bordered w-full h-32"
                  phx-change="form_field_changed"
                  phx-target={@myself}
                ><%= @publish_form["payload"] %></textarea>
              </div>
              
    <!-- QoS and Retain -->
              <div class="grid grid-cols-2 gap-4">
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">QoS Level</span>
                  </label>
                  <select
                    name="qos"
                    class="select select-bordered w-full"
                    phx-change="form_field_changed"
                    phx-target={@myself}
                  >
                    <option value="0" selected={@publish_form["qos"] == 0}>0 - At most once</option>
                    <option value="1" selected={@publish_form["qos"] == 1}>1 - At least once</option>
                    <option value="2" selected={@publish_form["qos"] == 2}>2 - Exactly once</option>
                  </select>
                </div>

                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">Retain Message</span>
                  </label>
                  <div class="flex items-center gap-2 mt-2">
                    <input
                      type="checkbox"
                      name="retain"
                      checked={@publish_form["retain"]}
                      class="toggle toggle-primary"
                      phx-change="form_field_changed"
                      phx-target={@myself}
                    />
                    <span class="label-text">Retain</span>
                  </div>
                </div>
              </div>
              
    <!-- MQTT 5.0 Properties Section -->
              <%= if show_mqtt5_properties?(@publish_form["client_id"], @active_broker_name) do %>
                <div class="divider">MQTT 5.0 Properties</div>
                
    <!-- Content Type and Payload Format Indicator -->
                <div class="grid grid-cols-2 gap-4">
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Content Type</span>
                    </label>
                    <input
                      type="text"
                      name="content_type"
                      value={@publish_form["content_type"]}
                      placeholder="e.g., application/json"
                      class="input input-bordered w-full"
                      phx-change="form_field_changed"
                      phx-target={@myself}
                    />
                  </div>

                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Payload Format</span>
                    </label>
                    <div class="flex items-center gap-2 mt-2">
                      <input
                        type="checkbox"
                        name="payload_format_indicator"
                        checked={@publish_form["payload_format_indicator"]}
                        class="toggle toggle-primary"
                        phx-change="form_field_changed"
                        phx-target={@myself}
                      />
                      <span class="label-text">UTF-8 String</span>
                    </div>
                  </div>
                </div>
                
    <!-- Message Expiry and Topic Alias -->
                <div class="grid grid-cols-2 gap-4">
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Message Expiry (seconds)</span>
                    </label>
                    <input
                      type="number"
                      name="message_expiry_interval"
                      value={@publish_form["message_expiry_interval"]}
                      placeholder="0 (no expiry)"
                      min="0"
                      class="input input-bordered w-full"
                      phx-change="form_field_changed"
                      phx-target={@myself}
                    />
                  </div>

                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Topic Alias</span>
                    </label>
                    <input
                      type="number"
                      name="topic_alias"
                      value={@publish_form["topic_alias"]}
                      placeholder="0 (no alias)"
                      min="0"
                      max="65535"
                      class="input input-bordered w-full"
                      phx-change="form_field_changed"
                      phx-target={@myself}
                    />
                  </div>
                </div>
                
    <!-- Response Topic and Correlation Data -->
                <div class="grid grid-cols-2 gap-4">
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Response Topic</span>
                    </label>
                    <input
                      type="text"
                      name="response_topic"
                      value={@publish_form["response_topic"]}
                      placeholder="Topic for response"
                      class="input input-bordered w-full"
                      phx-change="form_field_changed"
                      phx-target={@myself}
                    />
                  </div>

                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Correlation Data</span>
                    </label>
                    <input
                      type="text"
                      name="correlation_data"
                      value={@publish_form["correlation_data"]}
                      placeholder="Correlation identifier"
                      class="input input-bordered w-full"
                      phx-change="form_field_changed"
                      phx-target={@myself}
                    />
                  </div>
                </div>
                
    <!-- User Properties -->
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">User Properties</span>
                  </label>
                  <div class="space-y-2">
                    <%= for {property, index} <- Enum.with_index(@publish_form["user_properties"] || []) do %>
                      <div class="flex gap-2 items-center">
                        <input
                          type="text"
                          name={"user_property_key_#{index}"}
                          value={property["key"]}
                          placeholder="Property key"
                          class="input input-bordered flex-1"
                          phx-change="user_property_changed"
                          phx-value-index={index}
                          phx-value-field="key"
                          phx-target={@myself}
                        />
                        <input
                          type="text"
                          name={"user_property_value_#{index}"}
                          value={property["value"]}
                          placeholder="Property value"
                          class="input input-bordered flex-1"
                          phx-change="user_property_changed"
                          phx-value-index={index}
                          phx-value-field="value"
                          phx-target={@myself}
                        />
                        <div class="flex-shrink-0">
                          <%= if index == length(@publish_form["user_properties"]) - 1 do %>
                            <button
                              type="button"
                              class="btn btn-sm btn-primary"
                              phx-click="add_user_property"
                              phx-target={@myself}
                            >
                              <.icon name="hero-plus" class="size-4" />
                            </button>
                          <% else %>
                            <button
                              type="button"
                              class="btn btn-sm btn-error"
                              phx-click="remove_user_property"
                              phx-value-index={index}
                              phx-target={@myself}
                            >
                              <.icon name="hero-trash" class="size-4" />
                            </button>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
              
    <!-- Submit Button -->
              <div class="form-control w-full">
                <button type="submit" class="btn btn-primary">
                  <.icon name="hero-paper-airplane" class="size-4 mr-2" /> Send Message
                </button>
              </div>
            </.form>
          </div>
        </div>
      </dialog>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("close_modal", _params, socket) do
    send(self(), {:close_send_modal})
    {:noreply, socket}
  end

  @impl true
  def handle_event("client_selection_changed", %{"client_id" => client_id}, socket) do
    updated_form = Map.put(socket.assigns.publish_form, "client_id", client_id)
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("form_field_changed", params, socket) do
    # Update form state with changed field
    updated_form =
      Enum.reduce(params, socket.assigns.publish_form, fn {key, value}, acc ->
        case key do
          "qos" -> Map.put(acc, key, String.to_integer(value))
          "retain" -> Map.put(acc, key, value == "on")
          "payload_format_indicator" -> Map.put(acc, key, value == "on")
          _ -> Map.put(acc, key, value)
        end
      end)

    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event(
        "user_property_changed",
        %{"index" => index_str, "field" => field, "value" => value},
        socket
      ) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.publish_form["user_properties"] || []

    updated_properties =
      List.update_at(current_properties, index, fn property ->
        Map.put(property, field, value)
      end)

    updated_form = Map.put(socket.assigns.publish_form, "user_properties", updated_properties)
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    current_properties = socket.assigns.publish_form["user_properties"] || []
    new_properties = current_properties ++ [%{"key" => "", "value" => ""}]

    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.publish_form["user_properties"] || []

    new_properties = List.delete_at(current_properties, index)

    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("send_message", params, socket) do
    # Extract form parameters
    client_id = params["client_id"]
    topic = params["topic"]
    payload = params["payload"] || ""
    qos = String.to_integer(params["qos"] || "0")
    retain = params["retain"] == "on"

    # Update form state with all current values (including MQTT 5.0 properties)
    updated_form = update_form_with_params(socket.assigns.publish_form, params)

    socket = assign(socket, :publish_form, updated_form)

    # Validate required fields
    if client_id != "" && topic != "" do
      # Build MQTT 5.0 properties if the client supports it
      connected_clients = get_connected_clients(socket.assigns[:active_broker_name] || "")
      properties = build_mqtt5_publish_properties(params, connected_clients, client_id)

      # Prepare publish options
      publish_opts = [qos: qos, retain: retain]

      # Add properties if any
      publish_opts =
        if map_size(properties) > 0 do
          [{:properties, properties} | publish_opts]
        else
          publish_opts
        end

      # Attempt to publish the message
      case Mqttable.MqttClient.Manager.publish(client_id, topic, payload, publish_opts) do
        {:ok, packet_id} ->
          # Success - show success message but keep modal open and form data
          send(self(), {:message_sent_successfully, packet_id})
          {:noreply, socket}

        {:error, :not_connected} ->
          send(self(), {:message_send_error, "Client is not connected"})
          {:noreply, socket}

        {:error, _reason, error_message} ->
          send(self(), {:message_send_error, "Failed to send message: #{error_message}"})
          {:noreply, socket}
      end
    else
      # Validation failed
      send(self(), {:message_send_error, "Please fill in all required fields"})
      {:noreply, socket}
    end
  end

  # Helper Functions

  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload" => "",
      "qos" => 0,
      "retain" => false,
      # MQTT 5.0 properties
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => "",
      "topic_alias" => "",
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => [%{"key" => "", "value" => ""}]
    }
  end

  defp update_form_with_params(current_form, params) do
    # Update form with all parameters, handling type conversions
    updated_form =
      Enum.reduce(params, current_form, fn {key, value}, acc ->
        case key do
          "qos" -> Map.put(acc, key, String.to_integer(value))
          "retain" -> Map.put(acc, key, value == "on")
          "payload_format_indicator" -> Map.put(acc, key, value == "on")
          _ -> Map.put(acc, key, value)
        end
      end)

    # Handle user properties separately if they exist in params
    user_properties = extract_user_properties_from_params(params)

    if length(user_properties) > 0 do
      Map.put(updated_form, "user_properties", user_properties)
    else
      updated_form
    end
  end

  defp extract_user_properties_from_params(params) do
    # Extract user properties from form params
    params
    |> Enum.filter(fn {key, _value} -> String.starts_with?(key, "user_property_") end)
    |> Enum.group_by(fn {key, _value} ->
      # Extract index from key like "user_property_key_0" or "user_property_value_0"
      key
      |> String.split("_")
      |> List.last()
      |> String.to_integer()
    end)
    |> Enum.sort_by(fn {index, _} -> index end)
    |> Enum.map(fn {_index, properties} ->
      # Convert list of key-value pairs to a map
      Enum.reduce(properties, %{"key" => "", "value" => ""}, fn {param_key, value}, acc ->
        if String.contains?(param_key, "_key_") do
          Map.put(acc, "key", value)
        else
          Map.put(acc, "value", value)
        end
      end)
    end)
  end

  defp show_mqtt5_properties?(client_id, active_broker_name) do
    # Show MQTT 5.0 properties if client supports MQTT 5.0
    if client_id != "" do
      connected_clients = get_connected_clients(active_broker_name || "")

      case Enum.find(connected_clients, fn client -> client.client_id == client_id end) do
        %{mqtt_version: version} when version in ["5.0", "5"] -> true
        _ -> false
      end
    else
      false
    end
  end

  defp smart_client_selection(form, active_broker_name) do
    connected_clients = get_connected_clients(active_broker_name || "")
    current_client_id = form["client_id"]

    # Check if current client is still valid
    client_still_connected =
      Enum.any?(connected_clients, fn client ->
        client.client_id == current_client_id
      end)

    # If no client selected or current client disconnected, select first available
    if current_client_id == "" || !client_still_connected do
      case connected_clients do
        [first_client | _] -> Map.put(form, "client_id", first_client.client_id)
        [] -> form
      end
    else
      form
    end
  end

  defp get_connected_clients(_broker_name) do
    Mqttable.MqttClient.Manager.get_connected_clients()
  end

  defp build_mqtt5_publish_properties(params, connected_clients, client_id) do
    # Check if client supports MQTT 5.0
    client = Enum.find(connected_clients, fn c -> c.client_id == client_id end)

    case client do
      %{mqtt_version: version} when version in ["5.0", "5"] ->
        # Build MQTT 5.0 properties map
        properties = %{}

        # Add content type if provided
        properties =
          if params["content_type"] && params["content_type"] != "" do
            Map.put(properties, :content_type, params["content_type"])
          else
            properties
          end

        # Add payload format indicator
        properties =
          if params["payload_format_indicator"] == "on" do
            Map.put(properties, :payload_format_indicator, 1)
          else
            properties
          end

        # Add message expiry interval
        properties =
          if params["message_expiry_interval"] && params["message_expiry_interval"] != "" do
            case Integer.parse(params["message_expiry_interval"]) do
              {interval, ""} when interval > 0 ->
                Map.put(properties, :message_expiry_interval, interval)

              _ ->
                properties
            end
          else
            properties
          end

        # Add topic alias
        properties =
          if params["topic_alias"] && params["topic_alias"] != "" do
            case Integer.parse(params["topic_alias"]) do
              {alias, ""} when alias > 0 and alias <= 65535 ->
                Map.put(properties, :topic_alias, alias)

              _ ->
                properties
            end
          else
            properties
          end

        # Add response topic
        properties =
          if params["response_topic"] && params["response_topic"] != "" do
            Map.put(properties, :response_topic, params["response_topic"])
          else
            properties
          end

        # Add correlation data
        properties =
          if params["correlation_data"] && params["correlation_data"] != "" do
            Map.put(properties, :correlation_data, params["correlation_data"])
          else
            properties
          end

        # Add user properties
        user_properties = extract_user_properties_from_params(params)

        valid_user_properties =
          Enum.filter(user_properties, fn %{"key" => key, "value" => value} ->
            key != "" && value != ""
          end)

        properties =
          if length(valid_user_properties) > 0 do
            user_props_list =
              Enum.map(valid_user_properties, fn %{"key" => key, "value" => value} ->
                {key, value}
              end)

            Map.put(properties, :user_properties, user_props_list)
          else
            properties
          end

        properties

      _ ->
        # Client doesn't support MQTT 5.0 or not found
        %{}
    end
  end
end
